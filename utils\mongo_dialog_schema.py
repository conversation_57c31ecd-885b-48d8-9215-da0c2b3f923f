from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional

class DialogLog(BaseModel):
    session_id: str
    user_id: str
    duration: int  # in ms
    score: int  # 1-10
    agent_performance: str
    created_at: datetime = Field(default_factory=datetime.utcnow) 

class User(BaseModel):
    user_id: str
    # created_at and updated_at will be handled by MongoDB automatically
    call_count: int = 0  # Auto-incrementing field managed by database operations
    tags: Optional[list[str]] = Field(default_factory=list)
    voice_profile: Optional[dict] = Field(default_factory=dict)  # {emotion_pref, gender_pref, voice_id}
    preferred_language: Optional[str] = None
    default_intent: Optional[str] = None
    feedback_score_avg: Optional[float] = None
    schema_version: Optional[str] = None

class CallSession(BaseModel):
    session_id: str
    user_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    agent_profile_used: Optional[str] = None
    pipeline_id: Optional[str] = None
    states_visited: Optional[list[str]] = Field(default_factory=list)
    interrupts: Optional[list[str]] = Field(default_factory=list)
    outcome: Optional[str] = None  # success/failure/referred
    final_action: Optional[str] = None
    call_score: Optional[int] = None  # 0-10
    duration_sec: Optional[float] = None
    latency_avg_ms: Optional[float] = None
    tts_characters_used: Optional[int] = None
    schema_version: Optional[str] = None

class Pipeline(BaseModel):
    pipeline_id: str
    user_id: Optional[str] = None
    workflow_states: Optional[list[str]] = Field(default_factory=list)
    metadata: Optional[dict] = Field(default_factory=dict)  # {title, vertical, description}
    version: Optional[str] = None
    created_by: Optional[str] = None
    schema_version: Optional[str] = None

class IntentHistory(BaseModel):
    intent_id: str
    user_id: str
    session_id: str
    raw_input: str
    intent_detected: str
    state_id: str
    success: bool
    context_snapshot: Optional[dict] = Field(default_factory=dict)
    schema_version: Optional[str] = None 