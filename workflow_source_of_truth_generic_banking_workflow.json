{"id": "generic_banking_workflow", "name": "Generic Banking Support Flow", "version": "1.1", "states": {"state_greeting": {"type": "input", "layer2_id": "l2_greeting", "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "expected_input": ["intent", "slots"], "expected_output": ["acknowledgment", "transition_signal"]}, "state_check_balance": {"type": "inform", "layer2_id": "l2_check_balance", "allowed_tools": ["LLM", "TTS"], "expected_input": ["account_id"], "expected_output": ["account_balance"]}, "state_goodbye": {"type": "end", "layer2_id": "l2_goodbye", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["exit_signal"]}}, "rules": [{"from": "state_greeting", "condition": "intent == 'check_balance'", "to": "state_check_balance"}, {"from": "state_greeting", "condition": "intent == 'loan_inquiry'", "to": "state_loan_inquiry"}, {"from": "state_check_balance", "condition": "true", "to": "state_goodbye"}], "allowed_actions": ["Creating a new bank account", "Asking about exchange rates", "Checking account balance", "Requesting a loan", "Closing an account"], "prohibited_actions": ["Do not share account name and password", "Do not disclose sensitive information"], "allowed_tools": ["CACHE", "STT", "LLM", "TTS"]}