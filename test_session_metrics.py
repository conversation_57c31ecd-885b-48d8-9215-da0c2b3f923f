#!/usr/bin/env python3
"""
Test script to verify the session metrics calculation and data flow.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent)
sys.path.append(project_root)

from core.state_mgmt.memory_manager import MemoryManager

class MockContextualMemory:
    """Mock contextual memory for testing without Redis."""
    def __init__(self):
        self.data = {}

    async def get_all(self):
        return self.data

    async def get(self, key):
        return self.data.get(key)

    async def set(self, key, value):
        self.data[key] = value

async def test_session_metrics():
    """Test the session metrics calculation and data flow."""

    # Create a memory manager instance with mocked contextual memory
    session_id = "test_session_123"
    user_id = "test_user_456"
    memory_manager = MemoryManager(session_id=session_id, user_id=user_id)

    # Replace contextual memory with mock
    memory_manager.contextual = MockContextualMemory()

    # Mock the save methods to avoid MongoDB dependency
    async def mock_save_dialog_to_mongo(*args, **kwargs):
        print("Mock: Saved dialog to MongoDB")
        return "mock_dialog_id"

    async def mock_save_call_session(*args, **kwargs):
        print("Mock: Saved call session to MongoDB")
        return "mock_call_session_id"

    async def mock_save_user_profile(*args, **kwargs):
        print("Mock: Saved user profile to MongoDB")
        return "mock_user_id"

    async def mock_save_intent_history(*args, **kwargs):
        print("Mock: Saved intent history to MongoDB")
        return "mock_intent_id"

    # Replace the save methods
    memory_manager.save_call_session = mock_save_call_session
    memory_manager.save_user_profile = mock_save_user_profile
    memory_manager.save_intent_history = mock_save_intent_history
    
    # Simulate conversation data with latency information
    conversation = [
        {
            "role": "user",
            "text": "Check my balance",
            "timestamp": "2025-01-08T10:00:00Z",
            "user_id": user_id,
            "session_id": session_id
        },
        {
            "role": "ai",
            "text": "I'll check your account balance for you right away.",
            "timestamp": "2025-01-08T10:00:02Z",
            "user_id": user_id,
            "session_id": session_id,
            "latencySTT": 150,
            "latencyLLM": 800,
            "latencyTTS": 300,
            "total_latency": 1250
        },
        {
            "role": "user",
            "text": "Thank you",
            "timestamp": "2025-01-08T10:00:05Z",
            "user_id": user_id,
            "session_id": session_id
        },
        {
            "role": "ai",
            "text": "Your current balance is $2,500.00. Is there anything else I can help you with?",
            "timestamp": "2025-01-08T10:00:07Z",
            "user_id": user_id,
            "session_id": session_id,
            "latencySTT": 120,
            "latencyLLM": 900,
            "latencyTTS": 400,
            "total_latency": 1420
        }
    ]
    
    # Store conversation in contextual memory
    await memory_manager.contextual.set("conversation", conversation)
    await memory_manager.contextual.set("intent", "check_balance")
    await memory_manager.contextual.set("emotion", "neutral")

    # Store session metadata (as would be done by StateManager)
    await memory_manager.contextual.set("workflow_name", "GenericBank")
    await memory_manager.contextual.set("pipeline_id", "banking_workflow_v1")
    await memory_manager.contextual.set("states_visited", ["state_greeting", "state_check_balance"])
    await memory_manager.contextual.set("interrupts", [])
    await memory_manager.contextual.set("outcome", "completed")
    await memory_manager.contextual.set("final_action", "balance_provided")
    await memory_manager.contextual.set("session_start_time", "2025-01-08T10:00:00Z")
    await memory_manager.contextual.set("session_end_time", "2025-01-08T10:00:10Z")
    
    print("=== Testing Session Metrics Calculation ===")
    
    # Test the dialog log internal method
    print("\n1. Testing _save_dialog_log_internal()...")
    try:
        # Mock the file operations and MongoDB save
        import unittest.mock
        with unittest.mock.patch('builtins.open', unittest.mock.mock_open()):
            with unittest.mock.patch('os.getenv', return_value=None):  # No OpenAI key
                with unittest.mock.patch('utils.mongo_dialog_saver.save_dialog_to_mongo', return_value="mock_id"):
                    dialog_result = await memory_manager._save_dialog_log_internal()
                    print(f"Dialog result: {dialog_result}")
        
        # Verify expected metrics
        expected_tts_chars = len("I'll check your account balance for you right away.") + len("Your current balance is $2,500.00. Is there anything else I can help you with?")
        expected_total_latency = 1250 + 1420  # Sum of total_latency from AI turns
        expected_duration_sec = expected_total_latency / 1000
        
        print(f"Expected TTS characters: {expected_tts_chars}")
        print(f"Actual TTS characters: {dialog_result.get('tts_characters_used')}")
        print(f"Expected total latency: {expected_total_latency}ms")
        print(f"Actual total latency: {dialog_result.get('total_session_latency')}ms")
        print(f"Expected duration: {expected_duration_sec}s")
        print(f"Actual duration: {dialog_result.get('duration_sec')}s")
        
    except Exception as e:
        print(f"Error in dialog log test: {e}")
        dialog_result = None
    
    # Test the call session internal method
    print("\n2. Testing _save_call_session_internal()...")
    try:
        call_result = await memory_manager._save_call_session_internal(dialog_result)
        print(f"Call session saved successfully: {call_result}")
        
    except Exception as e:
        print(f"Error in call session test: {e}")
    
    # Test the unified save method
    print("\n3. Testing save_persistent_memory_data()...")
    try:
        await memory_manager.save_persistent_memory_data()
        print("All persistent memory data saved successfully!")
        
    except Exception as e:
        print(f"Error in unified save test: {e}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    asyncio.run(test_session_metrics())
