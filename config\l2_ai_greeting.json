{"id": "l2_try_agents", "version": "1.0", "pipeline": [{"step": "stt", "agent": "stt_agent", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "agent": "preprocessing_agent", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"fallback_message": "fallback_message", "emotion": "emotion", "gender": "gender", "intent": "intent", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}}, {"step": "filler_tts", "agent": "filler_tts_agent", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}}, {"step": "processing", "agent": "processing_agent", "input": {"text": "clean_text", "intent": "intent", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"route": "route", "data": "data", "llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "agent": "tts_agent", "input": {"text": "llm_answer"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}