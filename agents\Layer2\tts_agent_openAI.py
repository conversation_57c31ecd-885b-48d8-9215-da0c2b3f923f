from pathlib import Path
import sys
import os

project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from agents.base_agent import AudioAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
import asyncio
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from utils.audio_utils import synthesize_fallback_audio
from openai import AsyncOpenAI


class TTSAgentOpenAI(AudioAgent):
    def __init__(self, session_id=None, state_id=None):
        super().__init__(session_id, state_id)
        self.agent_name = "tts_agent"
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        self.client = AsyncOpenAI(api_key=self.openai_api_key)

    async def process(self, input_data, context=None):
        # Accepts either {"text": ...} or context with llm_answer
        text = input_data.get("text") if isinstance(input_data, dict) else None
        context = context or {}
        session_id = self.session_id or context.get("session_id")
        if not session_id:
            return StateOutput(
                status=StatusType.ERROR,
                message="No session_id provided",
                code=StatusCode.BAD_REQUEST,
                outputs={},
                meta={"agent": self.agent_name}
            )
        redis_key = str(session_id)
        shared_context = await self.load_context(redis_key) or {}
        fallback_result = await self.handle_redis_fallback(shared_context, session_id)
        if fallback_result:
            return fallback_result
        if not text:
            text = shared_context.get("llm_answer")
        if not text:
            return StateOutput(
                status=StatusType.ERROR,
                message="No text found for TTS",
                code=StatusCode.NOT_FOUND,
                outputs={},
                meta={"agent": self.agent_name}
            )
        emotion = shared_context.get("emotion", "neutral")
        gender = shared_context.get("gender", "female")
        voice_config = {"emotion": emotion, "gender": gender}
        return await self.text_to_speech(text, voice_config)

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def text_to_speech(self, text: str, voice_config: dict = None) -> StateOutput:
        start_time = asyncio.get_event_loop().time()
        self._log_process_start({"text": text, "voice_config": voice_config}, {})
        session_id = self.session_id
        if not session_id:
            return StateOutput(
                status=StatusType.ERROR,
                message="No session_id available",
                code=StatusCode.BAD_REQUEST,
                outputs={},
                meta={"agent": self.agent_name}
            )
        redis_key = str(session_id)
        try:
            if not text:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No text found for TTS",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"agent": self.agent_name}
                )
            emotion = (voice_config or {}).get("emotion", "neutral")
            gender = (voice_config or {}).get("gender", "female")
            audio_path = await self.synthesize_speech_openai(text, emotion, gender, session_id)

            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            outputs = {"audio_path": audio_path, "latencyTTS": duration_ms}
            # Update and save the shared context
            shared_context = await self.load_context(redis_key) or {}
            shared_context.update(outputs)
            await self.save_context(redis_key, shared_context)
            # Publish notification
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "complete"},
                context_keys_updated=list(outputs.keys())
            )
            await self.publish_notification("agent_completion", notification.to_dict())
            result = StateOutput(
                status=StatusType.SUCCESS,
                message="TTS processed successfully",
                code=StatusCode.OK,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencyTTS": duration_ms
                }
            )
            self._log_process_end(result, duration_ms)
            return result
        except Exception as e:
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            self._log_error(e, {"text": text, "voice_config": voice_config})
            # Notify orchestrator of error
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "error", "error_message": str(e)},
                context_keys_updated=[]
            )
            await self.publish_notification("agent_completion", notification.to_json())
            # Generate fallback audio
            fallback_text = "Sorry, something went wrong. Can you repeat that please?"
            audio_path = synthesize_fallback_audio(fallback_text, session_id=session_id or "fallback")
            outputs = {"fallback_message": fallback_text}
            if audio_path:
                outputs["audio_path"] = audio_path
            return StateOutput(
                status=StatusType.ERROR,
                message=f"TTSAgent error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencyTTS": duration_ms,
                    "error": str(e)
                }
            )

    def select_openai_voice(self, gender: str, emotion: str) -> str:
        """
        Select OpenAI TTS voice based on gender and emotion.
        OpenAI TTS supports: alloy, echo, fable, onyx, nova, shimmer
        """
        # Map gender and emotion to OpenAI voices
        voice_mapping = {
            "female": {
                "neutral": "nova",
                "happy": "shimmer", 
                "excited": "shimmer",
                "joyful": "shimmer",
                "surprised": "shimmer",
                "sad": "nova",
                "bored": "nova",
                "tired": "nova",
                "angry": "fable",
                "frustrated": "fable"
            },
            "male": {
                "neutral": "onyx",
                "happy": "echo",
                "excited": "echo",
                "joyful": "echo", 
                "surprised": "echo",
                "sad": "onyx",
                "bored": "onyx",
                "tired": "onyx",
                "angry": "alloy",
                "frustrated": "alloy"
            }
        }
        
        gender_lower = gender.lower()
        emotion_lower = emotion.lower()
        
        # Default to female/neutral if invalid inputs
        if gender_lower not in voice_mapping:
            gender_lower = "female"
        if emotion_lower not in voice_mapping[gender_lower]:
            emotion_lower = "neutral"
            
        return voice_mapping[gender_lower][emotion_lower]

    async def synthesize_speech_openai(self, text: str, emotion: str, gender: str, session_id: str) -> str:
        """Use OpenAI Text-to-Speech API to synthesize speech."""
        import os
        
        # Select voice based on gender and emotion
        voice = self.select_openai_voice(gender, emotion)
        
        # Generate speech using OpenAI TTS
        response = await self.client.audio.speech.create(
            model="tts-1",
            voice=voice,
            input=text
        )
        
        # Save to file
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
        audio_path = os.path.join(project_root, f"tts_output_{session_id}.mp3")
        
        # Write the audio content to file
        with open(audio_path, "wb") as out:
            out.write(response.content)
            
        return audio_path
