from utils.mongo_client import get_mongo_client
import os

async def save_dialog_to_mongo(session_id, user_id, duration, score, agent_performance):

    db = await get_mongo_client()
    collection = db["dialog"]
    doc = {
        "session_id": session_id,
        "user_id": user_id,
        "duration": duration,
        "score": score,
        "agent_performance": agent_performance or ""
    }
    result = await collection.insert_one(doc)
    return str(result.inserted_id)

async def save_user_to_mongo(user_id, created_at, last_seen, call_count=0, tags=None, voice_profile=None, preferred_language=None, default_intent=None, feedback_score_avg=None, schema_version=None):
    db = await get_mongo_client()
    collection = db["users"]
    doc = {
        "user_id": user_id,
        "created_at": created_at,
        "last_seen": last_seen,
        "call_count": call_count,
        "tags": tags or [],
        "voice_profile": voice_profile or {},
        "preferred_language": preferred_language,
        "default_intent": default_intent,
        "feedback_score_avg": feedback_score_avg,
        "schema_version": schema_version
    }
    result = await collection.insert_one(doc)
    return str(result.inserted_id)

async def save_call_session_to_mongo(session_id, user_id, timestamp, agent_profile_used=None, pipeline_id=None, states_visited=None, interrupts=None, outcome=None, final_action=None, call_score=None, duration_sec=None, latency_avg_ms=None, tts_characters_used=None, schema_version=None):
    db = await get_mongo_client()
    collection = db["call_sessions"]
    doc = {
        "session_id": session_id,
        "user_id": user_id,
        "timestamp": timestamp,
        "agent_profile_used": agent_profile_used,
        "pipeline_id": pipeline_id,
        "states_visited": states_visited or [],
        "interrupts": interrupts or [],
        "outcome": outcome,
        "final_action": final_action,
        "call_score": call_score,
        "duration_sec": duration_sec,
        "latency_avg_ms": latency_avg_ms,
        "tts_characters_used": tts_characters_used,
        "schema_version": schema_version
    }
    result = await collection.insert_one(doc)
    return str(result.inserted_id)

async def save_pipeline_to_mongo(pipeline_id, user_id=None, workflow_states=None, metadata=None, version=None, created_by=None, schema_version=None):
    db = await get_mongo_client()
    collection = db["pipelines"]
    doc = {
        "pipeline_id": pipeline_id,
        "user_id": user_id,
        "workflow_states": workflow_states or [],
        "metadata": metadata or {},
        "version": version,
        "created_by": created_by,
        "schema_version": schema_version
    }
    result = await collection.insert_one(doc)
    return str(result.inserted_id)

async def save_intent_history_to_mongo(intent_id, user_id, session_id, raw_input, intent_detected, state_id, success, context_snapshot=None, schema_version=None):
    db = await get_mongo_client()
    collection = db["intent_history"]
    doc = {
        "intent_id": intent_id,
        "user_id": user_id,
        "session_id": session_id,
        "raw_input": raw_input,
        "intent_detected": intent_detected,
        "state_id": state_id,
        "success": success,
        "context_snapshot": context_snapshot or {},
        "schema_version": schema_version
    }
    result = await collection.insert_one(doc)
    return str(result.inserted_id) 