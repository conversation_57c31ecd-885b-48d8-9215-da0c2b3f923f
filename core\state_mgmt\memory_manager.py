from datetime import datetime
import asyncio
import json
import logging
import sys
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
import redis
import os
import aiofiles  # Add this import at the top
import openai
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.logger_config import get_module_logger
from utils.redis_client import RedisClient
from utils.mongo_dialog_saver import save_dialog_to_mongo


# --- MemoryLayer Interface ---
class MemoryLayer(ABC):
    @abstractmethod
    async def get(self, key: str) -> Any:
        pass

    @abstractmethod
    async def set(self, key: str, value: Any):
        pass

# --- EphemeralMemory ---
class EphemeralMemory(MemoryLayer):
    """
    Stores transient data for a single pipeline step. Cleared after each pipeline.
    Thread-safe and async-compatible.
    """
    def __init__(self):
        self._data: Dict[str, Any] = {}
        self._lock = asyncio.Lock()  # Replace threading.Lock with asyncio.Lock
        self.logger = get_module_logger("EphemeralMemory")

    async def get(self, key: str) -> Any:
        async with self._lock:  # Use async context manager
            value = self._data.get(key)
            self.logger.debug(
                "GET ephemeral memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value},
                layer="memory_ephemeral"
            )
            return value

    async def set(self, key: str, value: Any):
        async with self._lock:  # Use async context manager
            self._data[key] = value
            self.logger.debug(
                "SET ephemeral memory",
                action="set",
                input_data={"key": key, "value": value},
                layer="memory_ephemeral"
            )

    async def clear(self):
        async with self._lock:  # Use async context manager
            self.logger.debug(
                "CLEAR ephemeral memory",
                action="clear",
                layer="memory_ephemeral"
            )
            self._data.clear()

    async def get_all(self) -> Dict[str, Any]:
        async with self._lock:  # Use async context manager
            return dict(self._data)

# --- ContextualMemory ---
class ContextualMemory(MemoryLayer):
    """
    Stores session-specific data (chat history, intents, slots, etc.) in Redis.
    Thread-safe and persistent.
    """
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.redis_client = RedisClient()
        self.logger = get_module_logger(f"ContextualMemory", session_id=session_id)

    async def get(self, key: str) -> Any:
        context = await self.redis_client.get(self.session_id) or {}
        return context.get(key)

    async def set(self, key: str, value: Any):
        context = await self.redis_client.get(self.session_id) or {}
        context[key] = value
        await self.redis_client.set(self.session_id, context)

    async def clear(self):
        await self.redis_client.set(self.session_id, {})

    async def get_all(self) -> Dict[str, Any]:
        return await self.redis_client.get(self.session_id) or {}

# --- PersistentMemory ---
class PersistentMemory(MemoryLayer):
    """
    Stores long-term data in Redis. Keys are prefixed by user_id or session_id.
    Serializes complex objects to JSON.
    """
    def __init__(self):
        self.redis_client = RedisClient()  # Use the async RedisClient instead of direct redis
        self.logger = get_module_logger("PersistentMemory")

    async def _make_key(self, prefix: str, key: str) -> str:
        return f"{prefix}:{key}"

    async def get(self, key: str) -> Any:
        try:
            value = await self.redis_client.get(key)  # Use async Redis client
            if value is None:
                self.logger.debug(
                    "GET persistent memory - not found",
                    action="get",
                    input_data={"key": key},
                    output_data={"value": None},
                    layer="memory_persistent"
                )
                return None
            
            self.logger.debug(
                "GET persistent memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value},
                layer="memory_persistent"
            )
            return value
        except Exception as e:
            self.logger.error(
                "Error getting persistent memory",
                action="get",
                input_data={"key": key},
                reason=str(e),
                layer="memory_persistent"
            )
            return None

    async def set(self, key: str, value: Any):
        """
        Set a value in persistent memory.
        - If value is a dict and existing value is a dict, merge (update) them.
        - If value is a list and existing value is a list, append new items.
        - Otherwise, overwrite as before.
        """
        try:
            existing = await self.get(key)  # Use await for async method
            # Merge dicts
            if isinstance(value, dict) and isinstance(existing, dict):
                merged = existing.copy()
                merged.update(value)
                value = merged
            # Append to lists
            elif isinstance(value, list) and isinstance(existing, list):
                value = existing + value
                
            await self.redis_client.set(key, value)  # Use async Redis client
            self.logger.debug(
                "SET persistent memory",
                action="set",
                input_data={"key": key, "value": value},
                layer="memory_persistent"
            )
        except Exception as e:
            self.logger.error(
                "Error setting persistent memory",
                action="set",
                input_data={"key": key},
                reason=str(e),
                layer="memory_persistent"
            )

    async def get_all(self, prefix: str) -> Dict[str, Any]:
        try:
            pattern = f"{prefix}:*"
            keys = await self.redis_client.client.keys(pattern)
            result = {}
            for key in keys:
                result[key] = await self.get(key)  # Use await for async method
            self.logger.debug(
                "GET_ALL persistent memory",
                action="get_all",
                input_data={"prefix": prefix},
                output_data={"result": result, "count": len(result)},
                layer="memory_persistent"
            )
            return result
        except Exception as e:
            self.logger.error(
                "Error getting all persistent memory",
                action="get_all",
                input_data={"prefix": prefix},
                reason=str(e),
                layer="memory_persistent"
            )
            return {}

# --- MemoryManager ---
class MemoryManager:
    """
    Orchestrates Ephemeral, Contextual, and Persistent memory layers.
    Provides unified API for get/set/clear and explicit memory saving.
    """
    def __init__(self, session_id: str, user_id: Optional[str] = None):
        self.session_id = session_id
        self.user_id = user_id or f"anon_{session_id}"
        self.ephemeral = EphemeralMemory()
        self.contextual = ContextualMemory(session_id)
        self.persistent = PersistentMemory()
        self.logger = get_module_logger("MemoryManager", session_id=session_id)

    async def get(self, key: str) -> Any:
        # Try ephemeral memory first
        value = await self.ephemeral.get(key)
        if value is not None:
            self.logger.debug(
                "GET from ephemeral memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value, "source": "ephemeral"},
                layer="memory_manager"
            )
            return value
        
        # Try contextual memory next
        value = await self.contextual.get(key)
        if value is not None:
            self.logger.debug(
                "GET from contextual memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value, "source": "contextual"},
                layer="memory_manager"
            )
            return value
        
        # Finally try persistent memory
        persistent_key = f"{self.user_id}:{key}"
        value = await self.persistent.get(persistent_key)
        if value is not None:
            self.logger.debug(
                "GET from persistent memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value, "source": "persistent"},
                layer="memory_manager"
            )
        else:
            self.logger.debug(
                "GET not found in any memory layer",
                action="get",
                input_data={"key": key},
                output_data={"value": None, "source": "none"},
                layer="memory_manager"
            )
        return value

    async def set(self, layer: str, key: str, value: Any):
        if layer == "ephemeral":
            await self.ephemeral.set(key, value)
        elif layer == "contextual":
            await self.contextual.set(key, value)
        elif layer == "persistent":
            persistent_key = f"{self.user_id}:{key}"
            await self.persistent.set(persistent_key, value)
        else:
            self.logger.error(
                "Unknown memory layer",
                action="set",
                input_data={"layer": layer, "key": key},
                reason=f"Unknown memory layer: {layer}",
                layer="memory_manager"
            )
            raise ValueError(f"Unknown memory layer: {layer}")
        self.logger.info(
            "SET memory value",
            action="set",
            input_data={"layer": layer, "key": key, "value": value},
            layer="memory_manager"
        )

    async def clear_ephemeral(self):
        self.logger.info(
            "Clearing ephemeral memory",
            action="clear_ephemeral",
            layer="memory_manager"
        )
        await self.ephemeral.clear()  # Use await for async method

    async def clear_contextual(self):
        self.logger.info(
            "Clearing contextual memory",
            action="clear_contextual",
            layer="memory_manager"
        )
        await self.contextual.clear()

    async def get_all_contextual(self) -> Dict[str, Any]:
        return await self.contextual.get_all()

    async def get_all_persistent(self) -> Dict[str, Any]:
        return await self.persistent.get_all(self.user_id)
    
    # get conversation history between ai and user
    async def get_conversation(self) -> list:
        context = await self.contextual.get_all()
        conversation = context.get("conversation", [])
        return conversation

    async def save_conversation_turn(self, user_message: str, ai_message: str, intent: Optional[str] = None, latency: Optional[dict] = None):
        """
        Async version: Save a conversation turn (user and AI message) to contextual memory.
        Args:
            user_message: The user's message
            ai_message: The AI's response
            intent: Optional detected intent
            latency: Optional dict of latency values for this turn (e.g., {'latencySTT': ..., ...})
        """
        conversation = await self.contextual.get("conversation") or []
        print("[DEBUG] About to save conversation:", conversation)
        print("[DEBUG] Type of conversation:", type(conversation))
        # Fetch the latest shared context and extract latency values
        context = await self.contextual.get_all()
        latency = {k: v for k, v in context.items() if k.startswith("latency") and isinstance(v, (int, float))}
        # Ensure latencyTTS is included if present
        if "latencyTTS" in context and isinstance(context["latencyTTS"], (int, float)):
            latency["latencyTTS"] = context["latencyTTS"]
        total_latency = sum(latency.values()) if latency else None
        # Add user turn
        user_turn = {
            "role": "user",
            "text": user_message,
            "timestamp": datetime.now().isoformat(),
            "user_id": self.user_id,
            "session_id": self.session_id
        }
        if latency:
            user_turn.update(latency)
            user_turn["total_latency"] = total_latency
        conversation.append(user_turn)
        # Add AI turn
        ai_turn = {
            "role": "ai",
            "text": ai_message,
            "timestamp": datetime.now().isoformat(),
            "user_id": self.user_id,
            "session_id": self.session_id
        }
        if latency:
            ai_turn.update(latency)
            ai_turn["total_latency"] = total_latency
        conversation.append(ai_turn)
        print("[DEBUG] Conversation after append:", conversation)
        await self.contextual.set("conversation", conversation)
        self.logger.info(
            "Saved conversation turn",
            action="save_conversation_turn",
            input_data={"user_message": user_message, "ai_message": ai_message, "intent": intent, "latency": latency},
            layer="memory_manager"
        )

    async def explicit_save(self, intent: str, slots: Dict[str, Any]):
        """
        Detects 'Remember this...' or explicit save intents and stores in persistent memory.
        Example: intent='save_preference', slots={'preference': 'language', 'value': 'en'}
        """
        if intent == "save_preference" and "preference" in slots and "value" in slots:
            key = slots["preference"]
            value = slots["value"]
            persistent_key = f"{self.user_id}:{key}"
            await self.persistent.set(persistent_key, value)  # Use await for async method
            self.logger.info(
                "Explicitly saved preference to persistent memory",
                action="explicit_save",
                input_data={"intent": intent, "slots": slots},
                output_data={"persistent_key": persistent_key, "value": value},
                layer="memory_manager"
            )
            return True
        self.logger.warning(
            "Explicit save intent not recognized or missing slots",
            action="explicit_save",
            input_data={"intent": intent, "slots": slots},
            reason="Intent not recognized or missing required slots",
            layer="memory_manager"
        )
        return False

    # TODO: Add support for advanced memory analytics and summarization in future versions.
    # def save_session_summary(self, llm_extract_func):
    #     """
    #     Extracts relevant information from contextual memory, sends it to an LLM for summarization/extraction,
    #     and saves the returned structured data to persistent memory. Call this before clearing contextual memory.
    #     Args:
    #         llm_extract_func: a function that takes session data (dict) and returns a dict of persistent info
    #     Returns:
    #         The dict of persistent info saved, for logging/debugging.
    #     """
    #     # 1. Extract contextual/session data
    #     session_data = self.get_all_contextual()
    #     self.logger.info(f"Extracting persistent info from session data for user {self.user_id}")

    #     # 2. Send to LLM for extraction 
    #     persistent_info = llm_extract_func(session_data)
    #     if not isinstance(persistent_info, dict):
    #         self.logger.error("LLM extraction did not return a dict. Nothing saved.")
    #         return None

    #     # 3. Save to persistent memory
    #     for key, value in persistent_info.items():
    #         self.set("persistent", key, value)
    #         self.logger.info(f"Saved to persistent memory: {key} = {value}")

    #     return persistent_info
    
    # def llm_extract_func(session_data):
    #     # Call your LLM here and return a dict of info to persist
    #     # For now, just a placeholder:
    #     return {"language": "en", "favorite_color": "blue"}

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def save_dialog_log(self):
        """
        Save the session dialog to logs/Dialog/{session_id}.txt with:
        1. LLM-generated summary
        2. Duration (from contextual/shared memory latency)
        3. LLM-generated learnings (bullet points)
        4. LLM-generated score (1-10)
        5. Conversation history in specified format
        """
        try:
            session_id = self.session_id
            dialog_dir = Path("logs/Dialog")
            dialog_dir.mkdir(parents=True, exist_ok=True)
            file_path = dialog_dir / f"{session_id}.txt"

            # Gather conversation and context
            context = await self.contextual.get_all()
            conversation = context.get("conversation", [])
            # Try to get latency/duration from context
            duration_ms = 0
            latency_details = {}
            for k in context:
                if k.startswith("latency") and isinstance(context[k], (int, float)):
                    duration_ms += context[k]
                    latency_details[k] = context[k]
            duration_sec = duration_ms / 1000 if duration_ms else None
            print(f"[Dialog Log] Latency details for session {session_id}: {latency_details}")

            # Format conversation for LLM prompt and for file
            formatted_conversation = []
            # For latency aggregation (AI turns only)
            latency_sums = {}
            total_session_latency = 0
            for i in range(1, len(conversation), 2):  # Only AI turns (odd indices)
                ai = conversation[i] if conversation[i].get("role") == "ai" else None
                if ai:
                    for k, v in ai.items():
                        if k.startswith("latency") and isinstance(v, (int, float)):
                            latency_sums[k] = latency_sums.get(k, 0) + v
                    if "total_latency" in ai and isinstance(ai["total_latency"], (int, float)):
                        total_session_latency += ai["total_latency"]
                if ai:
                    formatted_conversation.append({
                        "timestamp": ai.get("timestamp"),
                        "agent_response": ai.get("text"),
                        "emotion": context.get("emotion", "unknown"),
                        "intent": context.get("intent", "unknown"),
                        "latency": {k: ai.get(k) for k in ai if k.startswith("latency")},
                        "total_latency": ai.get("total_latency")
                    })
            print(f"[Dialog Log] Aggregated latency sums for session {session_id}: {latency_sums}")
            print(f"[Dialog Log] Total session latency: {total_session_latency}")

            # Prepare LLM prompt
            openai_api_key = os.getenv("OPENAI_API_KEY")
            if not openai_api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
            openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
            prompt = (
                "You are an expert evaluator and memory extractor for AI-human conversations. Given the following conversation, "
                "generate: 1) a summary of the conversation  in 2–4 sentences, highlighting the purpose of the call and key actions or issues discussed., 2) 3-5 bullet-point learnings Extract any relevant insights or personal information revealed by the user that can be stored as memory to personalize future interactions. These should be factual, context-relevant, and privacy-respectful. , "
                "3) Rate the conversation quality and the AI agent's performance on a scale of 1 to 10 (1 = very poor, 10 = excellent)., "
                "and 4) Briefly explain why the score was given, based on agent performance (e.g., clarity, helpfulness, understanding).\n"
                f"Conversation: {formatted_conversation}\n"
                "Respond in this format:\n"
                "Summary: ...\n"
                "Learnings: - {fact or preference about the user}- {goal, habit, or behavior}- {relevant context or constraint}  \n- ...\n- ...\n- ...\n"
                "Score: <number>\n"
                "Justification: ...\n"
                "\nRespond ONLY with a valid JSON object in this format (no extra text, markdown, or comments): {\"summary\": \"...\", \"learnings\": [\"...\", \"...\", \"...\"], \"score\": <number>, \"justification\": \"...\"}"
            )
            try:
                response = await openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=512,
                    temperature=0.3
                )
                llm_output = response.choices[0].message.content.strip()
                import json
                try:
                    llm_json = json.loads(llm_output)
                    summary = llm_json.get("summary")
                    learnings = llm_json.get("learnings")
                    score = llm_json.get("score")
                    justification = llm_json.get("justification")
                except Exception as e:
                    summary = learnings = score = justification = None
                    self.logger.error(f"Failed to parse LLM output as JSON: {e}. Output: {llm_output}")
            except Exception as e:
                llm_output = f"[LLM summary failed: {e}]"
                summary = learnings = score = justification = None

            # Write to file
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(f"Session ID: {session_id}\n")
                f.write(f"User ID: {self.user_id}\n")
                f.write(f"Aggregated Latency Sums: {latency_sums}\n")
                f.write(f"Duration: {total_session_latency} ms\n")
                f.write("\n=== LLM Session Summary ===\n")
                # Print summary, learnings, score, justification
                if summary:
                    f.write(f"Summary: {summary}\n")
                if learnings:
                    f.write("Learnings:\n")
                    if isinstance(learnings, list):
                        for line in learnings:
                            f.write(f"- {line}\n")
                    else:
                        f.write(str(learnings) + "\n")
                if score:
                    f.write(f"Score: {score}\n")
                if justification:
                    f.write(f"Justification: {justification}\n")
                f.write("\n=== Conversation (Readable) ===\n")
                # Write readable conversation (user and AI turns)
                for turn in conversation:
                    ts = turn.get("timestamp", "?")
                    role = turn.get("role", "?")
                    text = turn.get("text", "")
                    intent = turn.get("intent", context.get("intent", "unknown"))
                    emotion = context.get("emotion", "unknown")
                    latency = {k: v for k, v in turn.items() if k.startswith("latency")}
                    total_latency = turn.get("total_latency")
                    f.write(f"[{ts}] {role.upper()}: {text}\n  intent: {intent} | emotion: {emotion} | latency: {latency} | total_latency: {total_latency}\n")
            self.logger.info(
                "Saved dialog log to file",
                action="save_dialog_log",
                input_data={"file_path": str(file_path)},
                layer="memory_manager"
            )

            # Save to MongoDB
            try:
                await save_dialog_to_mongo(
                    session_id=session_id,
                    user_id=self.user_id,
                    duration=total_session_latency,
                    score=int(score) if score and str(score).isdigit() else None,
                    agent_performance=justification or ""
                )
                self.logger.info(
                    "Saved dialog log to MongoDB",
                    action="save_dialog_log_mongo",
                    input_data={"session_id": session_id, "user_id": self.user_id},
                    layer="memory_manager"
                )
            except Exception as e:
                self.logger.error(
                    f"Failed to save dialog log to MongoDB: {e}",
                    action="save_dialog_log_mongo",
                    input_data={"session_id": self.session_id},
                    reason=str(e),
                    layer="memory_manager"
                )
        except Exception as e:
            self.logger.error(
                f"Failed to save dialog log: {e}",
                action="save_dialog_log",
                input_data={"session_id": self.session_id},
                reason=str(e),
                layer="memory_manager"
            )

    async def persist_workflow_summary(self, workflow):
        """
        Extracts workflow information, rules, states, allowed actions, and disallowed/prohibited actions
        from the workflow object and saves them to persistent memory and as a JSON file.
        Args:
            workflow: WorkflowConfig or dict representing the workflow
        """
        # Support both pydantic model and dict
        if hasattr(workflow, 'dict'):
            workflow_dict = workflow.dict()
        else:
            workflow_dict = workflow
        workflow_id = workflow_dict.get("id")
        summary = {
            "id": workflow_dict.get("id"),
            "name": workflow_dict.get("name"),
            "version": workflow_dict.get("version"),
            "states": {},
            "rules": [],
            "allowed_actions": list(workflow_dict.get("allowed_actions", [])),
            "prohibited_actions": list(workflow_dict.get("prohibited_actions", [])),
        }
        states = workflow_dict.get("states", {})
        allowed_tools = set()
        for state_id, state in states.items():
            summary["states"][state_id] = {
                "type": state.get("type"),
                "layer2_id": state.get("layer2_id"),
                "allowed_tools": state.get("allowed_tools", []),
                "expected_input": state.get("expected_input", []),
                "expected_output": state.get("expected_output", [])
            }
            allowed_tools.update(state.get("allowed_tools", []))
            for transition in state.get("transitions", []):
                summary["rules"].append({
                    "from": state_id,
                    "condition": transition.get("condition"),
                    "to": transition.get("target")
                })
        summary["allowed_tools"] = list(allowed_tools)
        # Save to persistent memory (under key: workflow_source_of_truth_{workflow_id})
        await self.set("persistent", f"workflow_source_of_truth_{workflow_id}", summary)
        # Save as JSON file
        file_path = f"workflow_source_of_truth_{workflow_id}.json"
        import json
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        self.logger.info(
            "Persisted workflow summary to persistent memory and file",
            action="persist_workflow_source_of_truth",
            input_data={"file_path": file_path, "memory_key": f"workflow_source_of_truth_{workflow_id}"},
            layer="memory_manager"
        )

    async def save_user_profile(self, user_id, created_at, last_seen, call_count=0, tags=None, voice_profile=None, preferred_language=None, default_intent=None, feedback_score_avg=None, schema_version=None):
        from utils.mongo_dialog_saver import save_user_to_mongo
        try:
            result = await save_user_to_mongo(
                user_id=user_id,
                created_at=created_at,
                last_seen=last_seen,
                call_count=call_count,
                tags=tags,
                voice_profile=voice_profile,
                preferred_language=preferred_language,
                default_intent=default_intent,
                feedback_score_avg=feedback_score_avg,
                schema_version=schema_version
            )
            self.logger.info(
                "Saved user profile to MongoDB",
                action="save_user_profile",
                input_data={"user_id": user_id},
                output_data={"inserted_id": result},
                layer="memory_manager"
            )
            return result
        except Exception as e:
            self.logger.error(
                f"Failed to save user profile: {e}",
                action="save_user_profile",
                input_data={"user_id": user_id},
                reason=str(e),
                layer="memory_manager"
            )
            return None

    async def save_call_session(self, session_id, user_id, timestamp, agent_profile_used=None, pipeline_id=None, states_visited=None, interrupts=None, outcome=None, final_action=None, call_score=None, duration_sec=None, latency_avg_ms=None, tts_characters_used=None, schema_version=None):
        from utils.mongo_dialog_saver import save_call_session_to_mongo
        try:
            result = await save_call_session_to_mongo(
                session_id=session_id,
                user_id=user_id,
                timestamp=timestamp,
                agent_profile_used=agent_profile_used,
                pipeline_id=pipeline_id,
                states_visited=states_visited,
                interrupts=interrupts,
                outcome=outcome,
                final_action=final_action,
                call_score=call_score,
                duration_sec=duration_sec,
                latency_avg_ms=latency_avg_ms,
                tts_characters_used=tts_characters_used,
                schema_version=schema_version
            )
            self.logger.info(
                "Saved call session to MongoDB",
                action="save_call_session",
                input_data={"session_id": session_id, "user_id": user_id},
                output_data={"inserted_id": result},
                layer="memory_manager"
            )
            return result
        except Exception as e:
            self.logger.error(
                f"Failed to save call session: {e}",
                action="save_call_session",
                input_data={"session_id": session_id, "user_id": user_id},
                reason=str(e),
                layer="memory_manager"
            )
            return None

    async def save_pipeline(self, pipeline_id, user_id=None, workflow_states=None, metadata=None, version=None, created_by=None, schema_version=None):
        from utils.mongo_dialog_saver import save_pipeline_to_mongo
        try:
            result = await save_pipeline_to_mongo(
                pipeline_id=pipeline_id,
                user_id=user_id,
                workflow_states=workflow_states,
                metadata=metadata,
                version=version,
                created_by=created_by,
                schema_version=schema_version
            )
            self.logger.info(
                "Saved pipeline to MongoDB",
                action="save_pipeline",
                input_data={"pipeline_id": pipeline_id, "user_id": user_id},
                output_data={"inserted_id": result},
                layer="memory_manager"
            )
            return result
        except Exception as e:
            self.logger.error(
                f"Failed to save pipeline: {e}",
                action="save_pipeline",
                input_data={"pipeline_id": pipeline_id, "user_id": user_id},
                reason=str(e),
                layer="memory_manager"
            )
            return None

    async def save_intent_history(self, intent_id, user_id, session_id, raw_input, intent_detected, state_id, success, context_snapshot=None, schema_version=None):
        from utils.mongo_dialog_saver import save_intent_history_to_mongo
        try:
            result = await save_intent_history_to_mongo(
                intent_id=intent_id,
                user_id=user_id,
                session_id=session_id,
                raw_input=raw_input,
                intent_detected=intent_detected,
                state_id=state_id,
                success=success,
                context_snapshot=context_snapshot,
                schema_version=schema_version
            )
            self.logger.info(
                "Saved intent history to MongoDB",
                action="save_intent_history",
                input_data={"intent_id": intent_id, "user_id": user_id, "session_id": session_id},
                output_data={"inserted_id": result},
                layer="memory_manager"
            )
            return result
        except Exception as e:
            self.logger.error(
                f"Failed to save intent history: {e}",
                action="save_intent_history",
                input_data={"intent_id": intent_id, "user_id": user_id, "session_id": session_id},
                reason=str(e),
                layer="memory_manager"
            )
            return None

